import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'dart:developer' as developer;
import '../constants/app_constants.dart';
import '../models/user_model.dart';
import '../services/user_management_service.dart';

class UserRegistrationApprovalScreen extends StatefulWidget {
  const UserRegistrationApprovalScreen({super.key});

  @override
  State<UserRegistrationApprovalScreen> createState() => _UserRegistrationApprovalScreenState();
}

class _UserRegistrationApprovalScreenState extends State<UserRegistrationApprovalScreen> {
  List<UserModel> _pendingUsers = [];
  Map<String, int> _statistics = {};
  bool _isLoading = true;
  bool _showDebugInfo = false;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    developer.log('🔄 Loading pending registration data...', name: 'UserRegistrationApproval');

    setState(() {
      _isLoading = true;
    });

    try {
      developer.log('📡 Fetching pending users from service...', name: 'UserRegistrationApproval');
      final pendingUsers = await UserManagementService.getPendingRegistrationUsers();
      developer.log('✅ Fetched ${pendingUsers.length} pending users', name: 'UserRegistrationApproval');

      developer.log('📊 Fetching registration statistics...', name: 'UserRegistrationApproval');
      final statistics = await UserManagementService.getRegistrationStatistics();
      developer.log('✅ Statistics loaded: $statistics', name: 'UserRegistrationApproval');

      setState(() {
        _pendingUsers = pendingUsers;
        _statistics = statistics;
        _isLoading = false;
      });

      developer.log('🎯 Data loaded successfully. UI updated.', name: 'UserRegistrationApproval');
    } catch (e, stackTrace) {
      developer.log('❌ Error loading data: $e', name: 'UserRegistrationApproval', error: e, stackTrace: stackTrace);

      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading data: ${e.toString()}'),
            backgroundColor: AppConstants.errorColor,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('User Registration Approval'),
        backgroundColor: Theme.of(context).colorScheme.surface,
        elevation: 0,
        actions: [
          IconButton(
            icon: Icon(_showDebugInfo ? Icons.bug_report : Icons.bug_report_outlined),
            onPressed: () {
              setState(() {
                _showDebugInfo = !_showDebugInfo;
              });
              developer.log('🐛 Debug info toggled: $_showDebugInfo', name: 'UserRegistrationApproval');
            },
            tooltip: 'Toggle Debug Info',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // Debug Info Panel
                if (_showDebugInfo) _buildDebugInfoPanel(),

                // Statistics Cards
                _buildStatisticsCards(),

                // Pending Users List
                Expanded(
                  child: _pendingUsers.isEmpty
                      ? _buildEmptyState()
                      : _buildPendingUsersList(),
                ),
              ],
            ),
    );
  }

  Widget _buildStatisticsCards() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      child: Row(
        children: [
          Expanded(
            child: _buildStatCard(
              'Pending',
              _statistics['pending'] ?? 0,
              AppConstants.warningColor,
              Icons.hourglass_empty,
            ),
          ),
          const SizedBox(width: AppConstants.paddingMedium),
          Expanded(
            child: _buildStatCard(
              'Approved',
              _statistics['approved'] ?? 0,
              AppConstants.successColor,
              Icons.check_circle,
            ),
          ),
          const SizedBox(width: AppConstants.paddingMedium),
          Expanded(
            child: _buildStatCard(
              'Rejected',
              _statistics['rejected'] ?? 0,
              AppConstants.errorColor,
              Icons.cancel,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, int count, Color color, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: AppConstants.paddingSmall),
          Text(
            count.toString(),
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppConstants.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.check_circle_outline,
            size: 64,
            color: AppConstants.successColor,
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          Text(
            'No Pending Registrations',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Text(
            'All user registrations have been processed.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppConstants.textSecondaryColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildPendingUsersList() {
    return ListView.builder(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      itemCount: _pendingUsers.length,
      itemBuilder: (context, index) {
        final user = _pendingUsers[index];
        return _buildUserCard(user);
      },
    );
  }

  Widget _buildUserCard(UserModel user) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // User Info Header
            Row(
              children: [
                CircleAvatar(
                  radius: 25,
                  backgroundColor: AppConstants.primaryColor.withOpacity(0.1),
                  backgroundImage: user.profileImageUrl != null
                      ? NetworkImage(user.profileImageUrl!)
                      : null,
                  child: user.profileImageUrl == null
                      ? Text(
                          user.displayName.isNotEmpty
                              ? user.displayName[0].toUpperCase()
                              : user.username[0].toUpperCase(),
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            color: AppConstants.primaryColor,
                          ),
                        )
                      : null,
                ),
                const SizedBox(width: AppConstants.paddingMedium),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        user.displayName.isNotEmpty ? user.displayName : user.username,
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        '@${user.username}',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppConstants.textSecondaryColor,
                        ),
                      ),
                      Text(
                        user.email,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppConstants.textSecondaryColor,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppConstants.paddingSmall,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: AppConstants.warningColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                  ),
                  child: Text(
                    'PENDING',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppConstants.warningColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: AppConstants.paddingMedium),

            // Registration Date
            Row(
              children: [
                Icon(
                  Icons.calendar_today,
                  size: 16,
                  color: AppConstants.textSecondaryColor,
                ),
                const SizedBox(width: 4),
                Text(
                  'Registered: ${DateFormat('MMM dd, yyyy - HH:mm').format(user.createdAt)}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppConstants.textSecondaryColor,
                  ),
                ),
              ],
            ),

            const SizedBox(height: AppConstants.paddingMedium),

            // Action Buttons
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _approveUser(user),
                    icon: const Icon(Icons.check),
                    label: const Text('Approve'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppConstants.successColor,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: AppConstants.paddingMedium),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _rejectUser(user),
                    icon: const Icon(Icons.close),
                    label: const Text('Reject'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: AppConstants.errorColor,
                      side: const BorderSide(color: AppConstants.errorColor),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _approveUser(UserModel user) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Approve Registration'),
        content: Text(
          'Are you sure you want to approve ${user.displayName.isNotEmpty ? user.displayName : user.username}\'s registration?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              developer.log('✅ Approving user: ${user.email} (ID: ${user.id})', name: 'UserRegistrationApproval');
              try {
                await UserManagementService.approveUserRegistration(user.id);
                developer.log('✅ User approved successfully: ${user.email}', name: 'UserRegistrationApproval');
                _loadData();
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('${user.displayName.isNotEmpty ? user.displayName : user.username} approved successfully'),
                      backgroundColor: AppConstants.successColor,
                    ),
                  );
                }
              } catch (e, stackTrace) {
                developer.log('❌ Error approving user: $e', name: 'UserRegistrationApproval', error: e, stackTrace: stackTrace);
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Error approving user: ${e.toString()}'),
                      backgroundColor: AppConstants.errorColor,
                    ),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppConstants.successColor,
              foregroundColor: Colors.white,
            ),
            child: const Text('Approve'),
          ),
        ],
      ),
    );
  }

  void _rejectUser(UserModel user) {
    final reasonController = TextEditingController();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reject Registration'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Reject ${user.displayName.isNotEmpty ? user.displayName : user.username}\'s registration?'),
            const SizedBox(height: AppConstants.paddingMedium),
            TextField(
              controller: reasonController,
              decoration: const InputDecoration(
                labelText: 'Rejection Reason',
                hintText: 'Please provide a reason for rejection...',
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              final reason = reasonController.text.trim();
              if (reason.isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Please provide a rejection reason'),
                    backgroundColor: AppConstants.errorColor,
                  ),
                );
                return;
              }
              
              Navigator.of(context).pop();
              developer.log('❌ Rejecting user: ${user.email} (ID: ${user.id}) with reason: $reason', name: 'UserRegistrationApproval');
              try {
                await UserManagementService.rejectUserRegistration(user.id, reason);
                developer.log('❌ User rejected successfully: ${user.email}', name: 'UserRegistrationApproval');
                _loadData();
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('${user.displayName.isNotEmpty ? user.displayName : user.username}\'s registration rejected'),
                      backgroundColor: AppConstants.warningColor,
                    ),
                  );
                }
              } catch (e, stackTrace) {
                developer.log('❌ Error rejecting user: $e', name: 'UserRegistrationApproval', error: e, stackTrace: stackTrace);
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Error rejecting user: ${e.toString()}'),
                      backgroundColor: AppConstants.errorColor,
                    ),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppConstants.errorColor,
              foregroundColor: Colors.white,
            ),
            child: const Text('Reject'),
          ),
        ],
      ),
    );
  }

  Widget _buildDebugInfoPanel() {
    return Container(
      margin: const EdgeInsets.all(AppConstants.paddingMedium),
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
        border: Border.all(color: Colors.orange, width: 2),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.bug_report, color: Colors.orange),
              const SizedBox(width: 8),
              Text(
                'DEBUG INFO - PENDING REGISTRATIONS',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.orange[800],
                ),
              ),
            ],
          ),
          const SizedBox(height: AppConstants.paddingSmall),

          // Basic Info
          Text('🔄 Loading State: ${_isLoading ? "Loading..." : "Loaded"}'),
          Text('📊 Pending Users Count: ${_pendingUsers.length}'),
          Text('📈 Statistics: $_statistics'),
          Text('🕒 Last Updated: ${DateTime.now().toString()}'),

          const SizedBox(height: AppConstants.paddingSmall),
          const Divider(),

          // Service Debug Info
          Text('🔧 Service Debug Info:', style: TextStyle(fontWeight: FontWeight.bold)),
          Text('  - Collection: users'),
          Text('  - Query: registrationStatus == "pending"'),
          Text('  - Order: createdAt desc'),

          const SizedBox(height: AppConstants.paddingSmall),

          // Sample User Data
          if (_pendingUsers.isNotEmpty) ...[
            Text('👤 Sample User Data (First User):', style: TextStyle(fontWeight: FontWeight.bold)),
            Text('  - ID: ${_pendingUsers.first.id}'),
            Text('  - Email: ${_pendingUsers.first.email}'),
            Text('  - Username: ${_pendingUsers.first.username}'),
            Text('  - Display Name: ${_pendingUsers.first.displayName}'),
            Text('  - Registration Status: ${_pendingUsers.first.registrationStatus.value}'),
            Text('  - Is Active: ${_pendingUsers.first.isActive}'),
            Text('  - Is Verified: ${_pendingUsers.first.isVerified}'),
            Text('  - Role: ${_pendingUsers.first.role.value}'),
            Text('  - Created: ${_pendingUsers.first.createdAt}'),
            Text('  - Can Access App: ${_pendingUsers.first.canAccessApp}'),

            const SizedBox(height: AppConstants.paddingSmall),

            // All Users Summary
            Text('📋 All Pending Users Summary:', style: TextStyle(fontWeight: FontWeight.bold)),
            for (int i = 0; i < _pendingUsers.length && i < 5; i++) ...[
              Text('  ${i + 1}. ${_pendingUsers[i].email} (${_pendingUsers[i].registrationStatus.value})'),
            ],
            if (_pendingUsers.length > 5)
              Text('  ... and ${_pendingUsers.length - 5} more'),
          ] else ...[
            Text('❌ No pending users found'),
            Text('   This could mean:'),
            Text('   - All registrations are approved'),
            Text('   - No new registrations'),
            Text('   - Database query issue'),
          ],

          const SizedBox(height: AppConstants.paddingSmall),
          const Divider(),

          // Action Buttons for Debug
          Row(
            children: [
              ElevatedButton.icon(
                onPressed: () {
                  developer.log('🔄 Manual refresh triggered', name: 'UserRegistrationApproval');
                  _loadData();
                },
                icon: Icon(Icons.refresh),
                label: Text('Refresh'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                ),
              ),
              const SizedBox(width: 8),
              ElevatedButton.icon(
                onPressed: () {
                  developer.log('📋 Debug info copied to console', name: 'UserRegistrationApproval');
                  print('=== PENDING REGISTRATION DEBUG INFO ===');
                  print('Pending Users: ${_pendingUsers.length}');
                  print('Statistics: $_statistics');
                  for (var user in _pendingUsers) {
                    print('User: ${user.email} - Status: ${user.registrationStatus.value}');
                  }
                  print('=== END DEBUG INFO ===');
                },
                icon: Icon(Icons.copy),
                label: Text('Log Details'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
